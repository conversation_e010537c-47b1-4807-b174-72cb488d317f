import React, { useState, useEffect } from 'react';
import {
  BarChart3,
  Download,
  Calendar,
  TrendingUp,
  DollarSign,
  Users,
  Package,
  CreditCard,
  Filter,
  RefreshCw
} from 'lucide-react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Bar, Line, Doughnut } from 'react-chartjs-2';
import { useReports, SalesReport } from '../../hooks/useReports';
import { useAuth } from '../../contexts/AuthContext';
import InventoryAnalytics from './InventoryAnalytics';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const Reports: React.FC = () => {
  const { hasPermission } = useAuth();
  const { generateSalesReport, loading } = useReports();
  const [selectedPeriod, setSelectedPeriod] = useState<'today' | 'week' | 'month' | 'year'>('week');
  const [salesReport, setSalesReport] = useState<SalesReport | null>(null);
  const [customDateRange, setCustomDateRange] = useState({ start: '', end: '' });
  const [activeTab, setActiveTab] = useState<'sales' | 'inventory'>('sales');

  // Load initial report
  useEffect(() => {
    loadReport();
  }, [selectedPeriod]);

  const loadReport = async () => {
    try {
      const report = await generateSalesReport(selectedPeriod);
      setSalesReport(report);
    } catch (error) {
      console.error('Error loading report:', error);
    }
  };

  const handleCustomDateRange = async () => {
    if (customDateRange.start && customDateRange.end) {
      try {
        const report = await generateSalesReport(
          'custom',
          new Date(customDateRange.start),
          new Date(customDateRange.end)
        );
        setSalesReport(report);
      } catch (error) {
        console.error('Error loading custom report:', error);
      }
    }
  };

  const exportCSV = () => {
    if (!salesReport) return;

    // Create CSV content
    const csvContent = [
      ['Metric', 'Value'],
      ['Total Sales', `KSh ${salesReport.metrics.totalSales.toLocaleString()}`],
      ['Total Transactions', salesReport.metrics.totalTransactions.toString()],
      ['Average Transaction Value', `KSh ${salesReport.metrics.averageTransactionValue.toFixed(2)}`],
      ['Cash Sales', `KSh ${salesReport.metrics.cashSales.toLocaleString()}`],
      ['M-PESA Sales', `KSh ${salesReport.metrics.mpesaSales.toLocaleString()}`],
      ['Credit Sales', `KSh ${salesReport.metrics.creditSales.toLocaleString()}`],
      [],
      ['Top Products', ''],
      ...salesReport.topProducts.map(p => [p.name, `KSh ${p.revenue.toLocaleString()}`]),
      [],
      ['Top Services', ''],
      ...salesReport.topServices.map(s => [s.name, `KSh ${s.revenue.toLocaleString()}`]),
    ].map(row => row.join(',')).join('\n');

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `sales-report-${salesReport.period}-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  const exportPDF = async () => {
    if (!salesReport) return;

    try {
      // Create a temporary div with the report content
      const reportElement = document.createElement('div');
      reportElement.innerHTML = `
        <div style="padding: 20px; font-family: Arial, sans-serif;">
          <h1 style="color: #2563eb; margin-bottom: 20px;">Sales Report - ${salesReport.period}</h1>
          <p style="margin-bottom: 20px;">Generated on: ${new Date().toLocaleDateString()}</p>

          <h2 style="color: #374151; margin: 20px 0 10px 0;">Key Metrics</h2>
          <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
            <tr style="background-color: #f3f4f6;">
              <td style="padding: 8px; border: 1px solid #d1d5db; font-weight: bold;">Total Sales</td>
              <td style="padding: 8px; border: 1px solid #d1d5db;">KSh ${salesReport.metrics.totalSales.toLocaleString()}</td>
            </tr>
            <tr>
              <td style="padding: 8px; border: 1px solid #d1d5db; font-weight: bold;">Total Transactions</td>
              <td style="padding: 8px; border: 1px solid #d1d5db;">${salesReport.metrics.totalTransactions}</td>
            </tr>
            <tr style="background-color: #f3f4f6;">
              <td style="padding: 8px; border: 1px solid #d1d5db; font-weight: bold;">Average Transaction Value</td>
              <td style="padding: 8px; border: 1px solid #d1d5db;">KSh ${salesReport.metrics.averageTransactionValue.toFixed(2)}</td>
            </tr>
          </table>

          <h2 style="color: #374151; margin: 20px 0 10px 0;">Payment Methods</h2>
          <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
            <tr style="background-color: #f3f4f6;">
              <td style="padding: 8px; border: 1px solid #d1d5db; font-weight: bold;">Cash</td>
              <td style="padding: 8px; border: 1px solid #d1d5db;">KSh ${salesReport.metrics.cashSales.toLocaleString()}</td>
            </tr>
            <tr>
              <td style="padding: 8px; border: 1px solid #d1d5db; font-weight: bold;">M-PESA</td>
              <td style="padding: 8px; border: 1px solid #d1d5db;">KSh ${salesReport.metrics.mpesaSales.toLocaleString()}</td>
            </tr>
            <tr style="background-color: #f3f4f6;">
              <td style="padding: 8px; border: 1px solid #d1d5db; font-weight: bold;">Credit</td>
              <td style="padding: 8px; border: 1px solid #d1d5db;">KSh ${salesReport.metrics.creditSales.toLocaleString()}</td>
            </tr>
          </table>

          <h2 style="color: #374151; margin: 20px 0 10px 0;">Top Products</h2>
          <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
            <tr style="background-color: #f3f4f6;">
              <th style="padding: 8px; border: 1px solid #d1d5db; text-align: left;">Product</th>
              <th style="padding: 8px; border: 1px solid #d1d5db; text-align: left;">Quantity</th>
              <th style="padding: 8px; border: 1px solid #d1d5db; text-align: left;">Revenue</th>
            </tr>
            ${salesReport.topProducts.slice(0, 5).map(p => `
              <tr>
                <td style="padding: 8px; border: 1px solid #d1d5db;">${p.name}</td>
                <td style="padding: 8px; border: 1px solid #d1d5db;">${p.quantitySold}</td>
                <td style="padding: 8px; border: 1px solid #d1d5db;">KSh ${p.revenue.toLocaleString()}</td>
              </tr>
            `).join('')}
          </table>

          <h2 style="color: #374151; margin: 20px 0 10px 0;">Top Services</h2>
          <table style="width: 100%; border-collapse: collapse;">
            <tr style="background-color: #f3f4f6;">
              <th style="padding: 8px; border: 1px solid #d1d5db; text-align: left;">Service</th>
              <th style="padding: 8px; border: 1px solid #d1d5db; text-align: left;">Times Sold</th>
              <th style="padding: 8px; border: 1px solid #d1d5db; text-align: left;">Revenue</th>
            </tr>
            ${salesReport.topServices.slice(0, 5).map(s => `
              <tr>
                <td style="padding: 8px; border: 1px solid #d1d5db;">${s.name}</td>
                <td style="padding: 8px; border: 1px solid #d1d5db;">${s.timesSold}</td>
                <td style="padding: 8px; border: 1px solid #d1d5db;">KSh ${s.revenue.toLocaleString()}</td>
              </tr>
            `).join('')}
          </table>
        </div>
      `;

      // Temporarily add to DOM
      document.body.appendChild(reportElement);

      // Convert to canvas
      const canvas = await html2canvas(reportElement, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
      });

      // Remove from DOM
      document.body.removeChild(reportElement);

      // Create PDF
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF('p', 'mm', 'a4');
      const imgWidth = 210;
      const pageHeight = 295;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;

      let position = 0;

      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      pdf.save(`sales-report-${salesReport.period}-${new Date().toISOString().split('T')[0]}.pdf`);
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Error generating PDF. Please try again.');
    }
  };

  if (!hasPermission(['admin', 'attendant'])) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Users className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Access Denied</h3>
          <p className="mt-1 text-sm text-gray-500">
            You don't have permission to view reports.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <BarChart3 className="h-6 w-6 text-primary-600 mr-2" />
            <h1 className="text-2xl font-bold text-gray-900">Reports & Analytics</h1>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={loadReport}
              disabled={loading}
              className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
            <button
              onClick={exportCSV}
              disabled={!salesReport || activeTab !== 'sales'}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center disabled:opacity-50"
            >
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </button>
            <button
              onClick={exportPDF}
              disabled={!salesReport || activeTab !== 'sales'}
              className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center disabled:opacity-50"
            >
              <Download className="h-4 w-4 mr-2" />
              Export PDF
            </button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1 mb-6">
          <button
            onClick={() => setActiveTab('sales')}
            className={`px-4 py-2 rounded-lg text-sm font-medium ${
              activeTab === 'sales'
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Sales Analytics
          </button>
          <button
            onClick={() => setActiveTab('inventory')}
            className={`px-4 py-2 rounded-lg text-sm font-medium ${
              activeTab === 'inventory'
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Inventory Analytics
          </button>
        </div>

        {/* Period Selection - Only show for sales tab */}
        {activeTab === 'sales' && (
        <div className="flex flex-wrap items-center gap-4 mb-6">
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Period:</span>
          </div>
          <div className="flex space-x-2">
            {(['today', 'week', 'month', 'year'] as const).map((period) => (
              <button
                key={period}
                onClick={() => setSelectedPeriod(period)}
                className={`px-3 py-1 rounded-md text-sm font-medium ${
                  selectedPeriod === period
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {period.charAt(0).toUpperCase() + period.slice(1)}
              </button>
            ))}
          </div>

          {/* Custom Date Range */}
          <div className="flex items-center space-x-2 ml-4">
            <input
              type="date"
              value={customDateRange.start}
              onChange={(e) => setCustomDateRange(prev => ({ ...prev, start: e.target.value }))}
              className="border border-gray-300 rounded-md px-3 py-1 text-sm"
            />
            <span className="text-gray-500">to</span>
            <input
              type="date"
              value={customDateRange.end}
              onChange={(e) => setCustomDateRange(prev => ({ ...prev, end: e.target.value }))}
              className="border border-gray-300 rounded-md px-3 py-1 text-sm"
            />
            <button
              onClick={handleCustomDateRange}
              disabled={!customDateRange.start || !customDateRange.end}
              className="bg-primary-600 text-white px-3 py-1 rounded-md text-sm hover:bg-primary-700 disabled:opacity-50"
            >
              Apply
            </button>
          </div>
        </div>
        )}
      </div>

      {/* Content based on active tab */}
      {activeTab === 'inventory' ? (
        <InventoryAnalytics />
      ) : (
        <>

      {loading && (
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-center py-12">
            <RefreshCw className="h-8 w-8 text-primary-600 animate-spin mr-3" />
            <span className="text-lg text-gray-600">Generating report...</span>
          </div>
        </div>
      )}

      {!loading && !salesReport && (
        <div className="bg-white shadow rounded-lg p-6">
          <div className="text-center py-12">
            <BarChart3 className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No Data Available</h3>
            <p className="mt-1 text-sm text-gray-500">
              No transactions found for the selected period.
            </p>
          </div>
        </div>
      )}

      {!loading && salesReport && (
        <>
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white shadow rounded-lg p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <DollarSign className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total Sales</p>
                  <p className="text-2xl font-bold text-gray-900">
                    KSh {salesReport.metrics.totalSales.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white shadow rounded-lg p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <BarChart3 className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Transactions</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {salesReport.metrics.totalTransactions}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white shadow rounded-lg p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <TrendingUp className="h-8 w-8 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Avg. Transaction</p>
                  <p className="text-2xl font-bold text-gray-900">
                    KSh {salesReport.metrics.averageTransactionValue.toFixed(0)}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white shadow rounded-lg p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CreditCard className="h-8 w-8 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Payment Methods</p>
                  <p className="text-sm text-gray-600">
                    Cash: KSh {salesReport.metrics.cashSales.toLocaleString()}
                  </p>
                  <p className="text-sm text-gray-600">
                    M-PESA: KSh {salesReport.metrics.mpesaSales.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Charts Row */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Daily Sales Chart */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Daily Sales Trend</h3>
              <div className="h-64">
                <Line
                  data={{
                    labels: salesReport.dailyBreakdown.map(d =>
                      new Date(d.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
                    ),
                    datasets: [
                      {
                        label: 'Sales (KSh)',
                        data: salesReport.dailyBreakdown.map(d => d.sales),
                        borderColor: 'rgb(37, 99, 235)',
                        backgroundColor: 'rgba(37, 99, 235, 0.1)',
                        tension: 0.1,
                      },
                    ],
                  }}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: 'top' as const,
                      },
                    },
                    scales: {
                      y: {
                        beginAtZero: true,
                        ticks: {
                          callback: function(value) {
                            return 'KSh ' + Number(value).toLocaleString();
                          }
                        }
                      }
                    }
                  }}
                />
              </div>
            </div>

            {/* Payment Methods Chart */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Methods</h3>
              <div className="h-64">
                <Doughnut
                  data={{
                    labels: salesReport.paymentMethodBreakdown.map(p => {
                      switch(p.method) {
                        case 'cash': return 'Cash';
                        case 'mpesa': return 'M-PESA';
                        case 'debt': return 'Credit';
                        default: return p.method;
                      }
                    }),
                    datasets: [
                      {
                        data: salesReport.paymentMethodBreakdown.map(p => p.amount),
                        backgroundColor: [
                          'rgba(34, 197, 94, 0.8)',
                          'rgba(59, 130, 246, 0.8)',
                          'rgba(249, 115, 22, 0.8)',
                        ],
                        borderColor: [
                          'rgba(34, 197, 94, 1)',
                          'rgba(59, 130, 246, 1)',
                          'rgba(249, 115, 22, 1)',
                        ],
                        borderWidth: 2,
                      },
                    ],
                  }}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: 'bottom' as const,
                      },
                      tooltip: {
                        callbacks: {
                          label: function(context) {
                            const value = context.parsed;
                            const percentage = salesReport.paymentMethodBreakdown[context.dataIndex].percentage;
                            return `${context.label}: KSh ${value.toLocaleString()} (${percentage.toFixed(1)}%)`;
                          }
                        }
                      }
                    },
                  }}
                />
              </div>
            </div>
          </div>

          {/* Performance Tables */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Top Products */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Package className="h-5 w-5 mr-2 text-blue-600" />
                Top Products
              </h3>
              <div className="space-y-3">
                {salesReport.topProducts.length > 0 ? (
                  salesReport.topProducts.slice(0, 5).map((product, index) => (
                    <div key={product.id} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <span className="text-sm font-medium text-gray-500 w-6">
                          {index + 1}.
                        </span>
                        <div className="ml-2">
                          <p className="text-sm font-medium text-gray-900">{product.name}</p>
                          <p className="text-xs text-gray-500">Qty: {product.quantitySold}</p>
                        </div>
                      </div>
                      <span className="text-sm font-medium text-green-600">
                        KSh {product.revenue.toLocaleString()}
                      </span>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-gray-500">No product sales in this period</p>
                )}
              </div>
            </div>

            {/* Top Services */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <BarChart3 className="h-5 w-5 mr-2 text-purple-600" />
                Top Services
              </h3>
              <div className="space-y-3">
                {salesReport.topServices.length > 0 ? (
                  salesReport.topServices.slice(0, 5).map((service, index) => (
                    <div key={service.id} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <span className="text-sm font-medium text-gray-500 w-6">
                          {index + 1}.
                        </span>
                        <div className="ml-2">
                          <p className="text-sm font-medium text-gray-900">{service.name}</p>
                          <p className="text-xs text-gray-500">Times: {service.timesSold}</p>
                        </div>
                      </div>
                      <span className="text-sm font-medium text-green-600">
                        KSh {service.revenue.toLocaleString()}
                      </span>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-gray-500">No service sales in this period</p>
                )}
              </div>
            </div>

            {/* Staff Performance */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Users className="h-5 w-5 mr-2 text-orange-600" />
                Staff Performance
              </h3>
              <div className="space-y-3">
                {salesReport.attendantPerformance.length > 0 ? (
                  salesReport.attendantPerformance.slice(0, 5).map((attendant, index) => (
                    <div key={attendant.id} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <span className="text-sm font-medium text-gray-500 w-6">
                          {index + 1}.
                        </span>
                        <div className="ml-2">
                          <p className="text-sm font-medium text-gray-900">{attendant.name}</p>
                          <p className="text-xs text-gray-500">
                            {attendant.transactionCount} transactions
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className="text-sm font-medium text-green-600">
                          KSh {attendant.totalSales.toLocaleString()}
                        </span>
                        <p className="text-xs text-gray-500">
                          Avg: KSh {attendant.averageTransactionValue.toFixed(0)}
                        </p>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-gray-500">No staff data available</p>
                )}
              </div>
            </div>
          </div>
        </>
      )}
      )}
    </div>
  );
};

export default Reports;
