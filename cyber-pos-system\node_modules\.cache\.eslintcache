[{"E:\\FX\\Cyber POS\\cyber-pos-system\\src\\reportWebVitals.js": "1", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\index.tsx": "2", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\App.tsx": "3", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\contexts\\AuthContext.tsx": "4", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\auth\\Login.tsx": "5", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\dashboard\\Dashboard.tsx": "6", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POS.tsx": "7", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\Reports.tsx": "8", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\settings\\Settings.tsx": "9", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\Inventory.tsx": "10", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\layout\\Layout.tsx": "11", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\Services.tsx": "12", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\common\\LoadingSpinner.tsx": "13", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\config\\firebase.ts": "14", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\seedData.ts": "15", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\users\\UserManagement.tsx": "16", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useServices.ts": "17", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceModal.tsx": "18", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceCategories.tsx": "19", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceStats.tsx": "20", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\serviceUtils.ts": "21", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useProducts.ts": "22", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useCart.ts": "23", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POSCart.tsx": "24", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ServiceSelector.tsx": "25", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ProductSelector.tsx": "26", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\CheckoutModal.tsx": "27", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\ProductModal.tsx": "28", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\InventoryStats.tsx": "29", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\StockAdjustmentModal.tsx": "30", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\LowStockAlert.tsx": "31", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useTransactions.ts": "32", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\receiptGenerator.ts": "33", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useReports.ts": "34", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\testDataSeeder.ts": "35"}, {"size": 362, "mtime": 1751001516282, "results": "36", "hashOfConfig": "37"}, {"size": 550, "mtime": 1751002257124, "results": "38", "hashOfConfig": "37"}, {"size": 2402, "mtime": 1751003134738, "results": "39", "hashOfConfig": "37"}, {"size": 5912, "mtime": 1751002889593, "results": "40", "hashOfConfig": "37"}, {"size": 6144, "mtime": 1751003013744, "results": "41", "hashOfConfig": "37"}, {"size": 5937, "mtime": 1751001851404, "results": "42", "hashOfConfig": "37"}, {"size": 8045, "mtime": 1751003816943, "results": "43", "hashOfConfig": "37"}, {"size": 26568, "mtime": 1751007133642, "results": "44", "hashOfConfig": "37"}, {"size": 8562, "mtime": 1751007249570, "results": "45", "hashOfConfig": "37"}, {"size": 16613, "mtime": 1751004208648, "results": "46", "hashOfConfig": "37"}, {"size": 6483, "mtime": 1751001825178, "results": "47", "hashOfConfig": "37"}, {"size": 13480, "mtime": 1751003649429, "results": "48", "hashOfConfig": "37"}, {"size": 665, "mtime": 1751001779724, "results": "49", "hashOfConfig": "37"}, {"size": 2525, "mtime": 1751005564372, "results": "50", "hashOfConfig": "37"}, {"size": 7270, "mtime": 1751002967950, "results": "51", "hashOfConfig": "37"}, {"size": 12716, "mtime": 1751002934527, "results": "52", "hashOfConfig": "37"}, {"size": 4406, "mtime": 1751003274192, "results": "53", "hashOfConfig": "37"}, {"size": 9054, "mtime": 1751003411814, "results": "54", "hashOfConfig": "37"}, {"size": 3964, "mtime": 1751003442458, "results": "55", "hashOfConfig": "37"}, {"size": 8498, "mtime": 1751003577995, "results": "56", "hashOfConfig": "37"}, {"size": 5162, "mtime": 1751003518513, "results": "57", "hashOfConfig": "37"}, {"size": 6079, "mtime": 1751003779872, "results": "58", "hashOfConfig": "37"}, {"size": 5107, "mtime": 1751003753867, "results": "59", "hashOfConfig": "37"}, {"size": 15195, "mtime": 1751005588991, "results": "60", "hashOfConfig": "37"}, {"size": 13401, "mtime": 1751003915007, "results": "61", "hashOfConfig": "37"}, {"size": 10910, "mtime": 1751003957303, "results": "62", "hashOfConfig": "37"}, {"size": 11914, "mtime": 1751004034012, "results": "63", "hashOfConfig": "37"}, {"size": 12558, "mtime": 1751004256417, "results": "64", "hashOfConfig": "37"}, {"size": 11536, "mtime": 1751004300206, "results": "65", "hashOfConfig": "37"}, {"size": 10775, "mtime": 1751004342324, "results": "66", "hashOfConfig": "37"}, {"size": 12479, "mtime": 1751004391960, "results": "67", "hashOfConfig": "37"}, {"size": 5054, "mtime": 1751005576405, "results": "68", "hashOfConfig": "37"}, {"size": 7130, "mtime": 1751005110286, "results": "69", "hashOfConfig": "37"}, {"size": 10667, "mtime": 1751006877684, "results": "70", "hashOfConfig": "37"}, {"size": 7956, "mtime": 1751007184267, "results": "71", "hashOfConfig": "37"}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "do8dzb", {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\reportWebVitals.js", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\index.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\App.tsx", ["177"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\contexts\\AuthContext.tsx", ["178"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\auth\\Login.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\dashboard\\Dashboard.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POS.tsx", ["179", "180", "181", "182", "183", "184", "185", "186"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\Reports.tsx", ["187"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\settings\\Settings.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\Inventory.tsx", ["188", "189", "190", "191", "192"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\layout\\Layout.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\Services.tsx", ["193", "194", "195", "196"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\common\\LoadingSpinner.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\config\\firebase.ts", ["197", "198"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\seedData.ts", ["199", "200", "201"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\users\\UserManagement.tsx", ["202", "203"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useServices.ts", ["204", "205"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceModal.tsx", ["206"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceCategories.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceStats.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\serviceUtils.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useProducts.ts", ["207", "208"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useCart.ts", ["209", "210"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POSCart.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ServiceSelector.tsx", ["211"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ProductSelector.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\CheckoutModal.tsx", ["212"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\ProductModal.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\InventoryStats.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\StockAdjustmentModal.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\LowStockAlert.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useTransactions.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\receiptGenerator.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useReports.ts", ["213", "214", "215", "216", "217", "218"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\testDataSeeder.ts", [], [], {"ruleId": "219", "severity": 1, "message": "220", "line": 17, "column": 7, "nodeType": "221", "messageId": "222", "endLine": 17, "endColumn": 62}, {"ruleId": "219", "severity": 1, "message": "223", "line": 16, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 16, "endColumn": 8}, {"ruleId": "219", "severity": 1, "message": "224", "line": 10, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 10, "endColumn": 7}, {"ruleId": "219", "severity": 1, "message": "225", "line": 11, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 11, "endColumn": 13}, {"ruleId": "219", "severity": 1, "message": "226", "line": 12, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 12, "endColumn": 10}, {"ruleId": "219", "severity": 1, "message": "227", "line": 13, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 13, "endColumn": 9}, {"ruleId": "219", "severity": 1, "message": "228", "line": 14, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 14, "endColumn": 7}, {"ruleId": "219", "severity": 1, "message": "229", "line": 15, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 15, "endColumn": 8}, {"ruleId": "219", "severity": 1, "message": "230", "line": 21, "column": 10, "nodeType": "221", "messageId": "222", "endLine": 21, "endColumn": 17}, {"ruleId": "219", "severity": 1, "message": "231", "line": 21, "column": 19, "nodeType": "221", "messageId": "222", "endLine": 21, "endColumn": 26}, {"ruleId": null, "fatal": true, "severity": 2, "message": "232", "line": 647, "column": 7, "nodeType": null}, {"ruleId": "219", "severity": 1, "message": "233", "line": 6, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 6, "endColumn": 9}, {"ruleId": "219", "severity": 1, "message": "234", "line": 9, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 9, "endColumn": 15}, {"ruleId": "219", "severity": 1, "message": "235", "line": 10, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 10, "endColumn": 12}, {"ruleId": "219", "severity": 1, "message": "236", "line": 14, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 14, "endColumn": 11}, {"ruleId": "219", "severity": 1, "message": "237", "line": 15, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 15, "endColumn": 9}, {"ruleId": "219", "severity": 1, "message": "238", "line": 9, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 9, "endColumn": 13}, {"ruleId": "219", "severity": 1, "message": "239", "line": 10, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 10, "endColumn": 6}, {"ruleId": "219", "severity": 1, "message": "240", "line": 14, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 14, "endColumn": 4}, {"ruleId": "219", "severity": 1, "message": "241", "line": 15, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 15, "endColumn": 7}, {"ruleId": "219", "severity": 1, "message": "242", "line": 7, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 7, "endColumn": 27}, {"ruleId": "219", "severity": 1, "message": "243", "line": 10, "column": 22, "nodeType": "221", "messageId": "222", "endLine": 10, "endColumn": 44}, {"ruleId": "219", "severity": 1, "message": "224", "line": 13, "column": 10, "nodeType": "221", "messageId": "222", "endLine": 13, "endColumn": 14}, {"ruleId": "219", "severity": 1, "message": "230", "line": 13, "column": 16, "nodeType": "221", "messageId": "222", "endLine": 13, "endColumn": 23}, {"ruleId": "219", "severity": 1, "message": "231", "line": 13, "column": 25, "nodeType": "221", "messageId": "222", "endLine": 13, "endColumn": 32}, {"ruleId": "219", "severity": 1, "message": "227", "line": 8, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 8, "endColumn": 9}, {"ruleId": "244", "severity": 1, "message": "245", "line": 34, "column": 6, "nodeType": "246", "endLine": 34, "endColumn": 8, "suggestions": "247"}, {"ruleId": "219", "severity": 1, "message": "248", "line": 5, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 5, "endColumn": 10}, {"ruleId": "219", "severity": 1, "message": "223", "line": 11, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 11, "endColumn": 8}, {"ruleId": "219", "severity": 1, "message": "239", "line": 2, "column": 25, "nodeType": "221", "messageId": "222", "endLine": 2, "endColumn": 28}, {"ruleId": "219", "severity": 1, "message": "248", "line": 5, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 5, "endColumn": 10}, {"ruleId": "219", "severity": 1, "message": "223", "line": 11, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 11, "endColumn": 8}, {"ruleId": "219", "severity": 1, "message": "249", "line": 3, "column": 10, "nodeType": "221", "messageId": "222", "endLine": 3, "endColumn": 31}, {"ruleId": "244", "severity": 1, "message": "250", "line": 87, "column": 6, "nodeType": "246", "endLine": 87, "endColumn": 8, "suggestions": "251"}, {"ruleId": "219", "severity": 1, "message": "238", "line": 5, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 5, "endColumn": 13}, {"ruleId": "219", "severity": 1, "message": "252", "line": 9, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 9, "endColumn": 10}, {"ruleId": "219", "severity": 1, "message": "253", "line": 1, "column": 20, "nodeType": "221", "messageId": "222", "endLine": 1, "endColumn": 29}, {"ruleId": "219", "severity": 1, "message": "254", "line": 9, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 9, "endColumn": 13}, {"ruleId": "219", "severity": 1, "message": "255", "line": 10, "column": 3, "nodeType": "221", "messageId": "222", "endLine": 10, "endColumn": 8}, {"ruleId": "219", "severity": 1, "message": "231", "line": 13, "column": 23, "nodeType": "221", "messageId": "222", "endLine": 13, "endColumn": 30}, {"ruleId": "219", "severity": 1, "message": "230", "line": 13, "column": 32, "nodeType": "221", "messageId": "222", "endLine": 13, "endColumn": 39}, {"ruleId": "219", "severity": 1, "message": "224", "line": 13, "column": 41, "nodeType": "221", "messageId": "222", "endLine": 13, "endColumn": 45}, "@typescript-eslint/no-unused-vars", "'ProtectedRoute' is assigned a value but never used.", "Identifier", "unusedVar", "'where' is defined but never used.", "'User' is defined but never used.", "'CreditCard' is defined but never used.", "'Receipt' is defined but never used.", "'Trash2' is defined but never used.", "'Plus' is defined but never used.", "'Minus' is defined but never used.", "'Service' is defined but never used.", "'Product' is defined but never used.", "Parsing error: Unexpected token. Did you mean `{'}'}` or `&rbrace;`?", "'Filter' is defined but never used.", "'TrendingDown' is defined but never used.", "'BarChart3' is defined but never used.", "'Download' is defined but never used.", "'Upload' is defined but never used.", "'DollarSign' is defined but never used.", "'Tag' is defined but never used.", "'X' is defined but never used.", "'Save' is defined but never used.", "'connectFirestoreEmulator' is defined but never used.", "'connectStorageEmulator' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", "ArrayExpression", ["256"], "'getDocs' is defined but never used.", "'calculateServicePrice' is defined but never used.", "React Hook useCallback has a missing dependency: 'removeFromCart'. Either include it or remove the dependency array.", ["257"], "'Printer' is defined but never used.", "'useEffect' is defined but never used.", "'startAfter' is defined but never used.", "'limit' is defined but never used.", {"desc": "258", "fix": "259"}, {"desc": "260", "fix": "261"}, "Update the dependencies array to be: [loadUsers]", {"range": "262", "text": "263"}, "Update the dependencies array to be: [removeFromCart]", {"range": "264", "text": "265"}, [865, 867], "[loadUsers]", [2571, 2573], "[remove<PERSON><PERSON><PERSON><PERSON>]"]